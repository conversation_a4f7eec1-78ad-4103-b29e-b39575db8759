/* Import Google Fonts */
@import url("https://fonts.googleapis.com/css2?family=Merriweather:wght@400;700&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Josefin+Sans:ital,wght@0,400;1,400&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@400;600&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Battambang:wght@400;700&display=swap");

.homepage {
  position: relative;
  background-color: #fbfbf9;

  width: 100%;
  min-height: 100vh;
  /* Add enough height to accommodate all absolutely positioned content */
  height: auto;
  padding-bottom: 0;

  margin: 0 auto;
}

.homepage__hero-title {
  position: absolute;
  flex-shrink: 0;
  font-family: "Baskerville Old Face", "Times New Roman", serif;
  font-size: 96px;
  color: #966f33;
  height: 327px;
  line-height: 115.83px;
  left: 67px;
  top: 133px;
  width: 892px;
}

.homepage__features-section {
  display: flex;
  position: absolute;
  left: 56px;
  flex-direction: column;
  gap: 40px;
  align-items: flex-start;
  height: 277px;
  top: 509px;
  width: 310px;
}

.homepage__feature-item {
  display: flex;
  position: relative;
  gap: 20px;
  align-items: center;
}

.homepage__feature-icon {
  position: relative;
  height: 75px;
  width: 59px;
}

.homepage__feature-icon--medium {
  height: 63px;
  width: 59px;
}

.homepage__feature-icon--small {
  height: 59px;
  width: 59px;
}

.homepage__feature-content {
  display: flex;
  position: relative;
  flex-direction: column;
  gap: 10px;
  align-items: flex-start;
  width: 173px;
}

.homepage__feature-content--wide {
  width: 233px;
}

.homepage__feature-title {
  position: relative;
  align-self: stretch;
  font-family: "Battambang", sans-serif;
  font-size: 24px;
  letter-spacing: -0.05em;
  line-height: 24px;
  color: #966f33;
}

.homepage__feature-description {
  position: relative;
  align-self: stretch;
  font-family: "Source Sans Pro", sans-serif;
  font-size: 18px;
  letter-spacing: -0.05em;
  line-height: 20px;
  color: black;
}

.homepage__main-section {
  display: flex;
  position: absolute;
  flex-direction: column;
  gap: 80px;
  align-items: flex-start;
  height: 441px;
  left: 1087px;
  top: 346px;
  width: 950px;
}

.homepage__main-title {
  position: relative;
  align-self: stretch;
  font-family: "Baskerville Old Face", "Times New Roman", serif;
  font-size: 72px;
  letter-spacing: -0.05em;
  color: black;
  height: 155px;
  line-height: 65.28px;
}

.homepage__main-content {
  display: flex;
  position: relative;
  flex-direction: column;
  gap: 0;
  align-items: flex-start;
  width: 797px;
}

.homepage__main-quote {
  position: relative;
  align-self: stretch;
  font-family: "Josefin Sans", sans-serif;
  font-size: 32px;
  font-style: italic;
  letter-spacing: -0.05em;
  line-height: 32px;
  color: black;
  height: 209px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .homepage {
    width: 100%;
    height: auto;
    min-height: 100vh;
  }

  .homepage__main-wrapper {
    min-height: 1000px;
  }

  .homepage__hero-title {
    height: auto;
    font-size: 72px;
    left: 5%;
    top: 150px;
    width: 90%;
  }

  .homepage__features-section {
    height: auto;
    left: 5%;
    top: 400px;
    width: 90%;
  }

  .homepage__main-section {
    height: auto;
    left: 5%;
    top: 700px;
    width: 90%;
  }

  .homepage__main-title {
    height: auto;
    font-size: 48px;
  }

  .homepage__main-content {
    gap: 20px;
    width: 100%;
  }

  .homepage__main-quote {
    height: auto;
    font-size: 24px;
  }
}

@media (max-width: 640px) {
  .homepage__main-wrapper {
    min-height: 800px;
  }

  .homepage__hero-title {
    font-size: 48px;
    top: 120px;
  }

  .homepage__features-section {
    top: 300px;
  }

  .homepage__main-section {
    top: 600px;
  }

  .homepage__main-title {
    font-size: 36px;
  }

  .homepage__main-quote {
    font-size: 18px;
  }

  .homepage__feature-item {
    flex-direction: column;
    text-align: center;
  }
}

/* Main content wrapper to contain absolutely positioned elements */
.homepage__main-wrapper {
  position: relative;
  width: 100%;
  /* Set minimum height to accommodate all absolutely positioned content */
  min-height: 900px;
  height: auto;
}

/* Content Creation & Research Engine Section */
.homepage__content-engine-section {
  overflow: hidden;
  position: relative;
  margin: 50px auto 0;
  max-width: 1920px;
  background-color: #fbf8f1;
  min-height: 871px;
  width: 100%;
  display: flex;
  align-items: center;
}

.homepage__content-engine-main-image {
  object-fit: cover;
  position: absolute;
  left: 0;
  top: 0;
  flex-shrink: 0;
  height: 100%;
  width: 48.6%;
  max-width: 933px;
}

.homepage__content-engine-ratings {
  display: inline-flex;
  position: absolute;
  gap: 6px;
  align-items: center;
  height: 37px;
  right: 64px;
  bottom: 94px;
  width: 124px;
}

.homepage__content-engine-rating-star {
  object-fit: cover;
  position: relative;
  aspect-ratio: 38/37;
  height: 37px;
  width: 38px;
}

.homepage__content-engine-content {
  display: flex;
  position: absolute;
  flex-direction: column;
  gap: 56px;
  align-items: flex-start;
  right: 434px;
  top: 50%;
  transform: translateY(-50%);
  width: 498px;
  z-index: 2;
}

.homepage__content-engine-title {
  position: relative;
  align-self: stretch;
  font-size: 72px;
  color: black;
  line-height: 78.39px;
}

.homepage__content-engine-title-text {
  font-size: 72px;
  color: black;
}

.homepage__content-engine-description {
  position: relative;
  font-size: 32px;
  font-weight: 300;
  line-height: 36px;
  color: black;
  width: 480px;
}

.homepage__content-engine-description-text {
  font-size: 32px;
  color: black;
}

/* Responsive styles for Content Creation & Research Engine Section */
@media (max-width: 768px) {
  .homepage__content-engine-section {
    flex-direction: column;
    padding: 40px 20px;
    min-height: 600px;
    margin-top: 50px;
  }

  .homepage__content-engine-main-image {
    position: relative;
    width: 60%;
    height: auto;
    max-height: 500px;
    margin: 0 auto 30px;
  }

  .homepage__content-engine-ratings {
    position: relative;
    right: auto;
    bottom: auto;
    justify-content: center;
    margin: 20px auto;
  }

  .homepage__content-engine-content {
    position: relative;
    right: auto;
    top: auto;
    transform: none;
    gap: 32px;
    margin: 0 auto;
    width: 100%;
    max-width: 600px;
    text-align: center;
  }

  .homepage__content-engine-title {
    font-size: 48px;
  }

  .homepage__content-engine-title-text {
    font-size: 48px;
  }

  .homepage__content-engine-description {
    width: 100%;
    font-size: 24px;
  }

  .homepage__content-engine-description-text {
    font-size: 24px;
  }
}

/* Why Us Section */
.homepage__why-us-section {
  box-sizing: border-box;
  position: relative;
  padding: 80px 112px 0;
  margin: 0 auto;
  width: 100%;
  max-width: none;
  background-color: #f5f5f4;
  min-height: 871px;
}

.homepage__why-us-title {
  position: absolute;
  font-size: 128px;
  color: #966f33;
  height: 117px;
  line-height: 117px;
  left: 110px;
  top: 78px;
  width: 377px;
}

.homepage__why-us-content {
  display: inline-flex;
  position: absolute;
  top: 0;
  gap: 64px;
  align-items: center;
  height: 889px;
  left: 276px;
  width: 1469px;
}

.homepage__why-us-image {
  object-fit: cover;
  position: relative;
  aspect-ratio: 764/889;
  height: 889px;
  width: 764px;
}

.homepage__why-us-features {
  display: flex;
  position: relative;
  flex-direction: column;
  gap: 20px;
  align-items: flex-start;
  width: 643px;
}

.homepage__feature-item-container {
  position: relative;
  width: 100%;
}

.homepage__feature-item-header {
  display: flex;
  position: relative;
  gap: 14px;
  align-items: flex-start;
  height: 42px;
}

.homepage__feature-item-icon {
  position: relative;
  width: 42px;
  height: 42px;
  flex-shrink: 0;
}

.homepage__feature-item-title {
  font-size: 36px;
  letter-spacing: -0.025em;
  line-height: 36px;
  color: #966f33;
  height: 35px;
}

.homepage__feature-item-description {
  margin-top: 12px;
  margin-left: 56px;
  font-size: 20px;
  line-height: 24px;
  color: black;
  width: 481px;
}

/* Responsive styles for Why Us Section */
@media (max-width: 768px) {
  .homepage__why-us-section {
    padding: 40px 20px 0;
    max-width: 991px;
  }

  .homepage__why-us-title {
    left: 20px;
    top: 40px;
    font-size: 96px;
    height: 70px;
    width: 300px;
  }

  .homepage__why-us-content {
    position: static;
    flex-direction: column;
    gap: 40px;
    align-items: center;
    width: 100%;
    height: auto;
  }

  .homepage__why-us-image {
    width: 100%;
    height: auto;
    max-width: 500px;
  }

  .homepage__why-us-features {
    gap: 24px;
    width: 100%;
    max-width: 600px;
  }

  .homepage__feature-item-description {
    margin-left: 56px;
    width: 100%;
    font-size: 18px;
  }

  .homepage__feature-item-title {
    font-size: 30px;
  }
}

@media (max-width: 640px) {
  .homepage__why-us-section {
    padding: 20px 16px 0;
    max-width: 100vw;
  }

  .homepage__why-us-title {
    top: 20px;
    font-size: 64px;
    height: 50px;
    left: 15px;
    width: 250px;
  }

  .homepage__why-us-content {
    gap: 32px;
  }

  .homepage__why-us-image {
    max-width: 100%;
  }

  .homepage__why-us-features {
    gap: 20px;
  }

  .homepage__feature-item-header {
    gap: 10px;
  }

  .homepage__feature-item-title {
    font-size: 24px;
    line-height: 28px;
  }

  .homepage__feature-item-description {
    margin-left: 44px;
    font-size: 16px;
    line-height: 20px;
  }
}

@media (max-width: 640px) {
  .homepage__content-engine-section {
    padding: 16px;
    max-width: 100%;
  }

  .homepage__content-engine-main-image {
    width: 100%;
    height: auto;
    max-height: 300px;
  }

  .homepage__content-engine-ratings {
    gap: 4px;
  }

  .homepage__content-engine-rating-star {
    width: 28px;
    height: 28px;
  }

  .homepage__content-engine-content {
    gap: 20px;
  }

  .homepage__content-engine-title {
    font-size: 36px;
  }

  .homepage__content-engine-title-text {
    font-size: 36px;
  }

  .homepage__content-engine-description {
    font-size: 20px;
  }

  .homepage__content-engine-description-text {
    font-size: 20px;
  }
}
