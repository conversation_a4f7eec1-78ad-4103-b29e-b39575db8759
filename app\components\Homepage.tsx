import Navbar from "./Navbar";
import Button from "./Button";
import WhyUs from "./WhyUs";
import Testimonials from "./Testimonials";
import "./Homepage.css";

export default function Homepage() {
  const handleJoinWaitlist = () => {
    // Handle join waitlist action
    console.log("Join waitlist clicked");
  };

  const features = [
    {
      icon: "https://api.builder.io/api/v1/image/assets/TEMP/aa858a2f8ed0a134930f72e7fef50e364573ebfc?width=118",
      title: "Features",
      description: "Benefit of joining waiting list",
      iconClass: "homepage__feature-icon",
    },
    {
      icon: "https://api.builder.io/api/v1/image/assets/TEMP/9badbe856312fc48be0805e349db264b09d2846c?width=118",
      title: "Why You should Join us",
      description: "Urgency of joining waiting list",
      iconClass: "homepage__feature-icon homepage__feature-icon--medium",
      contentClass: "homepage__feature-content homepage__feature-content--wide",
    },
    {
      icon: "https://api.builder.io/api/v1/image/assets/TEMP/189e56502619bfcd3df1b9c9dfa220747ca30a92?width=118",
      title: "Testimonials",
      description: "Benefit of joining waiting list",
      iconClass: "homepage__feature-icon homepage__feature-icon--small",
    },
  ];

  return (
    <div className="homepage">
      <Navbar onJoinWaitlist={handleJoinWaitlist} />

      <div className="homepage__main-wrapper">
        <div className="homepage__hero-title">
          Be a legal
          <br />
          entreprenuer
        </div>

        <div className="homepage__features-section">
          {features.map((feature, index) => (
            <div key={index} className="homepage__feature-item">
              <img src={feature.icon} alt="" className={feature.iconClass} />
              <div
                className={feature.contentClass || "homepage__feature-content"}
              >
                <div className="homepage__feature-title">{feature.title}</div>
                <div className="homepage__feature-description">
                  {feature.description}
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="homepage__main-section">
          <div className="homepage__main-title">
            Creator platform for modern
            <br />
            --- legal professionals
          </div>

          <div className="homepage__main-content">
            <div className="homepage__main-quote">
              &ldquo;LawVriksh is the first AI-powered platform that helps legal
              experts build a respected online voice, create high-impact
              content, and unlock new monetization opportunities.&rdquo;
            </div>
            <Button size="large" onClick={handleJoinWaitlist}>
              Join Waitlist
            </Button>
          </div>
        </div>
      </div>

      {/* Content Creation & Research Engine Section */}
      <div className="homepage__content-engine-section">
        <div>
          <div
            dangerouslySetInnerHTML={{
              __html:
                '<svg id="206:417" width="1917" height="871" viewBox="0 0 1917 871" fill="none" xmlns="http://www.w3.org/2000/svg" class="background-blur" style="width: 1917px; height: 871px; flex-shrink: 0; fill: #FFF8E4; backdrop-filter: blur(23.75px); position: absolute; left: 1px; top: 0px"> <foreignObject x="-47.5" y="-47.5" width="2012" height="966"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(23.75px);clip-path:url(#bgblur_0_206_417_clip_path);height:100%;width:100%"></div></foreignObject><path data-figma-bg-blur-radius="47.5" d="M0 0H1917V871H0V0Z" fill="#FFF8E4"></path> <defs> <clipPath id="bgblur_0_206_417_clip_path" transform="translate(47.5 47.5)"><path d="M0 0H1917V871H0V0Z"></path> </clipPath></defs> </svg>',
            }}
          />
        </div>
        <img
          src="https://api.builder.io/api/v1/image/assets/TEMP/4b873ad28146576aa6665f9b53b842d3a81ac038?width=1866"
          alt=""
          className="homepage__content-engine-main-image"
        />
        <div className="homepage__content-engine-ratings">
          <img
            src="https://api.builder.io/api/v1/image/assets/TEMP/862c345882334d9eb32a40f2d45f9ec9b9265f5e?width=76"
            alt=""
            className="homepage__content-engine-rating-star"
          />
          <img
            src="https://api.builder.io/api/v1/image/assets/TEMP/b7ce36a2cc500c00afe8f5345e96fe7c90c26e87?width=76"
            alt=""
            className="homepage__content-engine-rating-star"
          />
          <img
            src="https://api.builder.io/api/v1/image/assets/TEMP/b7ce36a2cc500c00afe8f5345e96fe7c90c26e87?width=76"
            alt=""
            className="homepage__content-engine-rating-star"
          />
        </div>
        <div className="homepage__content-engine-content">
          <div className="homepage__content-engine-title">
            <div className="homepage__content-engine-title-text">
              Content Creation &amp; Research Engine
            </div>
          </div>
          <div className="homepage__content-engine-description">
            <div className="homepage__content-engine-description-text">
              AI-Powered Writing Tools with legal-specific training and
              compliance checks
            </div>
          </div>
        </div>
      </div>

      {/* Why us? Section */}
      <WhyUs />

      {/* Customer Testimonials Section */}
      <Testimonials />

      {/* Team Testimonial Section */}
      <div className="homepage__team-testimonial-section">
        <div className="homepage__team-testimonial-background">
          <div
            dangerouslySetInnerHTML={{
              __html:
                '<svg id="210:579" width="799" height="871" viewBox="0 0 799 871" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" class="background-svg" style="width: 799px; height: 871px; display: block"> <foreignObject x="0" y="0" width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(0px);clip-path:url(#bgblur_0_210_579_clip_path);height:100%;width:100%"></div></foreignObject><path data-figma-bg-blur-radius="0" d="M0 0H525V871H0V0Z" fill="#966F33"></path> <foreignObject x="340.3" y="128.8" width="469.4" height="613.4"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(5.35px);clip-path:url(#bgblur_1_210_579_clip_path);height:100%;width:100%"></div></foreignObject><path data-figma-bg-blur-radius="10.7" d="M351 139.5H799V731.5H351V139.5Z" fill="#F9EDE9"></path> <rect x="351" y="139.5" width="448" height="592" fill="url(#pattern0_210_579)"></rect> <defs> <clipPath id="bgblur_0_210_579_clip_path" transform="translate(0 0)"><path d="M0 0H525V871H0V0Z"></path> </clipPath><clipPath id="bgblur_1_210_579_clip_path" transform="translate(-340.3 -128.8)"><path d="M351 139.5H799V731.5H351V139.5Z"></path> </clipPath><pattern id="pattern0_210_579" patternContentUnits="objectBoundingBox" width="1" height="1"> <use xlink:href="#image0_210_579" transform="matrix(0.00172797 0 0 0.00130828 -0.426075 -0.278796)"></use> </pattern>  </defs> </svg>',
            }}
          />
        </div>
        <div className="homepage__team-testimonial-content">
          <div className="homepage__team-testimonial-ratings">
            <img
              src="https://api.builder.io/api/v1/image/assets/TEMP/862c345882334d9eb32a40f2d45f9ec9b9265f5e?width=76"
              alt=""
              className="homepage__team-testimonial-star"
            />
            <img
              src="https://api.builder.io/api/v1/image/assets/TEMP/b7ce36a2cc500c00afe8f5345e96fe7c90c26e87?width=76"
              alt=""
              className="homepage__team-testimonial-star"
            />
            <img
              src="https://api.builder.io/api/v1/image/assets/TEMP/b7ce36a2cc500c00afe8f5345e96fe7c90c26e87?width=76"
              alt=""
              className="homepage__team-testimonial-star"
            />
            <img
              src="https://api.builder.io/api/v1/image/assets/TEMP/b7ce36a2cc500c00afe8f5345e96fe7c90c26e87?width=76"
              alt=""
              className="homepage__team-testimonial-star"
            />
            <img
              src="https://api.builder.io/api/v1/image/assets/TEMP/b7ce36a2cc500c00afe8f5345e96fe7c90c26e87?width=76"
              alt=""
              className="homepage__team-testimonial-star"
            />
          </div>
          <div className="homepage__team-testimonial-text-content">
            <div className="homepage__team-testimonial-title">
              What our team has to say!
            </div>
            <div className="homepage__team-testimonial-quote">
              &quot;Amplify the digital footprint of contributing legal experts.
              Detailed author profiles and multi-channel promotion boost their
              professional authority.&quot;
            </div>
          </div>
          <div className="homepage__team-testimonial-attribution">
            <div className="homepage__team-testimonial-author">
              This is Sahil Saurav. Meet the Whole team
            </div>
            <div className="homepage__team-testimonial-divider" />
            <div className="homepage__team-testimonial-arrow">
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M5 12H19M19 12L12 5M19 12L12 19"
                  stroke="black"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
